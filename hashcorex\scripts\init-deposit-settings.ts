import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function initDepositSettings() {
  console.log('🔧 Initializing deposit settings...');

  try {
    // Initialize deposit settings with default values (Shasta Testnet)
    const depositSettings = [
      { key: 'usdtDepositAddress', value: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs' }, // Shasta Testnet USDT contract address
      { key: 'minDepositAmount', value: '10' },
      { key: 'maxDepositAmount', value: '10000' },
      { key: 'depositEnabled', value: 'true' },
      { key: 'minConfirmations', value: '1' },
      { key: 'depositFeePercentage', value: '0' },
      // Also set UPPER_CASE versions for compatibility
      { key: 'USDT_DEPOSIT_ADDRESS', value: 'TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs' },
      { key: 'MIN_DEPOSIT_AMOUNT', value: '10' },
      { key: 'MAX_DEPOSIT_AMOUNT', value: '10000' },
      { key: 'DEPOSIT_ENABLED', value: 'true' },
      { key: 'MIN_CONFIRMATIONS', value: '1' },
      { key: 'DEPOSIT_FEE_PERCENTAGE', value: '0' },
    ];

    console.log('📝 Creating/updating admin settings...');
    for (const setting of depositSettings) {
      await prisma.adminSettings.upsert({
        where: { key: setting.key },
        update: { value: setting.value },
        create: {
          key: setting.key,
          value: setting.value,
        },
      });
      console.log(`✅ Set ${setting.key} = ${setting.value}`);
    }

    console.log('✨ Deposit settings initialized successfully!');
    console.log('\n📋 Current deposit settings:');
    
    const allSettings = await prisma.adminSettings.findMany({
      where: {
        key: {
          in: depositSettings.map(s => s.key)
        }
      }
    });

    allSettings.forEach(setting => {
      console.log(`  ${setting.key}: ${setting.value}`);
    });

  } catch (error) {
    console.error('❌ Error initializing deposit settings:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

initDepositSettings();
