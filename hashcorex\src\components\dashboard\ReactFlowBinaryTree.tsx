'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  ReactFlow,
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  ConnectionMode,
  Panel,
  MiniMap,
  Handle,
  Position,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Card, CardHeader, CardTitle, CardContent, Button } from '@/components/ui';
import { 
  Users, 
  Copy, 
  Share2, 
  TrendingUp, 
  Award, 
  RefreshCw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Eye,
  EyeOff
} from 'lucide-react';
import { formatCurrency, formatDate, copyToClipboard } from '@/lib/utils';
import { BinaryPointsInfoPanel } from './BinaryPointsInfoPanel';

interface BinaryTreeNode {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    isActive: boolean;
  };
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  directReferralCount: number;
  teamCounts: {
    left: number;
    right: number;
    total: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  hasLeftChild?: boolean;
  hasRightChild?: boolean;
  leftChild?: BinaryTreeNode;
  rightChild?: BinaryTreeNode;
}

interface BinaryTreeData {
  treeStructure: BinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

// Custom node component
const BinaryTreeNodeComponent = ({ data }: { data: any }) => {
  const { user, teamCounts, binaryPoints, hasLeftChild, hasRightChild, onExpand, isExpanded } = data;

  return (
    <div className="relative">
      {/* Input handle (top) */}
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        style={{ background: '#000', width: 8, height: 8 }}
      />

      <div className="bg-white border-2 border-gray-200 rounded-lg p-3 shadow-lg min-w-[200px]">
        <div className="text-center">
          <div className={`w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center text-white font-bold ${
            user.isActive ? 'bg-green-500' : 'bg-gray-400'
          }`}>
            {user.firstName?.[0]}{user.lastName?.[0]}
          </div>
          <h3 className="font-semibold text-sm text-gray-900">
            {user.firstName} {user.lastName}
          </h3>
          <p className="text-xs text-gray-500 truncate">{user.email}</p>
          <div className="mt-2 text-xs">
            <div className="flex justify-between">
              <span>Left: {teamCounts.left}</span>
              <span>Right: {teamCounts.right}</span>
            </div>
            <div className="text-center mt-1 font-medium">
              Total: {teamCounts.total}
            </div>
            {binaryPoints && (
              <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                <div className="flex justify-between text-green-600">
                  <span>L: {binaryPoints.leftPoints}</span>
                  <span>R: {binaryPoints.rightPoints}</span>
                </div>
                <div className="text-center text-purple-600 font-medium">
                  Matched: {binaryPoints.matchedPoints}
                </div>
              </div>
            )}
          </div>
          {(hasLeftChild || hasRightChild) && (
            <button
              onClick={() => onExpand(user.id)}
              className="mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </button>
          )}
        </div>
      </div>

      {/* Output handles (bottom left and right) */}
      {hasLeftChild && (
        <Handle
          type="source"
          position={Position.Bottom}
          id="left"
          style={{
            background: '#10b981',
            width: 8,
            height: 8,
            left: '25%'
          }}
        />
      )}

      {hasRightChild && (
        <Handle
          type="source"
          position={Position.Bottom}
          id="right"
          style={{
            background: '#f59e0b',
            width: 8,
            height: 8,
            left: '75%'
          }}
        />
      )}
    </div>
  );
};

const nodeTypes = {
  binaryNode: BinaryTreeNodeComponent,
};

export const ReactFlowBinaryTree: React.FC = () => {
  const [treeData, setTreeData] = useState<BinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [showInactive, setShowInactive] = useState(true);
  
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  const fetchTreeData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/referrals/tree?depth=5&enhanced=true&expanded=${Array.from(expandedNodes).join(',')}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
          generateNodesAndEdges(data.data.treeStructure);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary tree data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateNodesAndEdges = useCallback((treeStructure: BinaryTreeNode) => {
    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];
    const nodeSpacing = { x: 250, y: 150 };

    const processNode = (node: BinaryTreeNode, x: number, y: number, level: number = 0) => {
      if (!showInactive && !node.user.isActive) return;

      const nodeId = node.user.id;
      const isExpanded = expandedNodes.has(nodeId);

      // Create the node
      newNodes.push({
        id: nodeId,
        type: 'binaryNode',
        position: { x, y },
        data: {
          ...node,
          onExpand: handleNodeExpand,
          isExpanded,
        },
      });

      // Process children if expanded or if it's the root level
      if (level === 0 || isExpanded) {
        if (node.leftChild) {
          const leftX = x - nodeSpacing.x * Math.pow(0.7, level);
          const leftY = y + nodeSpacing.y;
          
          processNode(node.leftChild, leftX, leftY, level + 1);
          
          // Add edge from parent to left child
          newEdges.push({
            id: `${nodeId}-${node.leftChild.user.id}`,
            source: nodeId,
            sourceHandle: 'left',
            target: node.leftChild.user.id,
            targetHandle: 'top',
            type: 'default',
            style: {
              stroke: '#10b981',
              strokeWidth: 3,
            },
            animated: false,
            label: 'Left',
            labelStyle: {
              fontSize: 10,
              fontWeight: 'bold',
              fill: '#10b981',
            },
          });
        }

        if (node.rightChild) {
          const rightX = x + nodeSpacing.x * Math.pow(0.7, level);
          const rightY = y + nodeSpacing.y;
          
          processNode(node.rightChild, rightX, rightY, level + 1);
          
          // Add edge from parent to right child
          newEdges.push({
            id: `${nodeId}-${node.rightChild.user.id}`,
            source: nodeId,
            sourceHandle: 'right',
            target: node.rightChild.user.id,
            targetHandle: 'top',
            type: 'default',
            style: {
              stroke: '#f59e0b',
              strokeWidth: 3,
            },
            animated: false,
            label: 'Right',
            labelStyle: {
              fontSize: 10,
              fontWeight: 'bold',
              fill: '#f59e0b',
            },
          });
        }
      }
    };

    // Start processing from root node at center
    processNode(treeStructure, 400, 50);

    // Debug: Log generated nodes and edges
    console.log('Generated nodes:', newNodes.length);
    console.log('Generated edges:', newEdges.length);
    if (newEdges.length > 0) {
      console.log('Sample edge:', newEdges[0]);
    }

    setNodes(newNodes);
    setEdges(newEdges);
  }, [expandedNodes, showInactive]);

  const handleNodeExpand = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  useEffect(() => {
    fetchTreeData();
  }, [expandedNodes]);

  useEffect(() => {
    if (treeData) {
      generateNodesAndEdges(treeData.treeStructure);
    }
  }, [treeData, generateNodesAndEdges]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-400" />
      </div>
    );
  }

  if (!treeData) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Tree Data</h3>
        <p className="text-gray-500">Unable to load your binary tree structure.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Direct Referrals</p>
                <p className="text-2xl font-bold">{treeData.statistics.totalDirectReferrals}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Left Side</p>
                <p className="text-lg font-bold text-green-600">{treeData.statistics.leftReferrals || 0} users</p>
                <p className="text-sm text-green-500">{treeData.statistics.binaryPoints.leftPoints} points</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Right Side</p>
                <p className="text-lg font-bold text-orange-600">{treeData.statistics.rightReferrals || 0} users</p>
                <p className="text-sm text-orange-500">{treeData.statistics.binaryPoints.rightPoints} points</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Binary Points</p>
                <p className="text-lg font-bold text-purple-600">{treeData.statistics.binaryPoints.matchedPoints} matched</p>
                <p className="text-sm text-purple-500">
                  {Math.min(treeData.statistics.binaryPoints.leftPoints, treeData.statistics.binaryPoints.rightPoints)} available
                </p>
              </div>
              <Award className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tree Visualization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Binary Tree Structure</span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setShowInactive(!showInactive)}
              >
                {showInactive ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showInactive ? 'Hide Inactive' : 'Show Inactive'}
              </Button>
              <Button size="sm" onClick={fetchTreeData}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ height: '600px', width: '100%' }}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              nodeTypes={nodeTypes}
              connectionMode={ConnectionMode.Loose}
              fitView
              fitViewOptions={{ padding: 0.2 }}
              proOptions={{ hideAttribution: true }}
              nodesDraggable={false}
              nodesConnectable={false}
              elementsSelectable={true}
            >
              <Background color="#f1f5f9" gap={20} />
              <Controls />
              <MiniMap
                nodeColor="#10b981"
                maskColor="rgba(0, 0, 0, 0.2)"
                style={{ backgroundColor: '#f8fafc' }}
              />
            </ReactFlow>
          </div>
        </CardContent>
      </Card>

      {/* Referral Links */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Share2 className="h-5 w-5 text-blue-500" />
            <span>Referral Links</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700">General Referral Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.general}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.general)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">Left Side Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.left}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.left)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700">Right Side Link</label>
              <div className="flex mt-1">
                <input
                  type="text"
                  value={treeData.referralLinks.right}
                  readOnly
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                />
                <Button
                  size="sm"
                  onClick={() => handleCopyLink(treeData.referralLinks.right)}
                  className="rounded-l-none"
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          <div className="mt-4 space-y-3">
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg
                for optimal network balance. Use specific side links to target placement.
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-xs text-green-700">
                <strong>Binary Matching:</strong> Points are matched weekly on Saturdays at 15:00 UTC.
                Each $1 investment by your downline = 1 binary point. Maximum 2,000 points per side.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Binary Points Information Panel */}
      <BinaryPointsInfoPanel />
    </div>
  );
};
